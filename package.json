{"name": "solnic-dev-blog", "version": "1.0.0", "description": "<PERSON>'s blog built with <PERSON>", "scripts": {"ghost-export": "node scripts/ghost.js --export-posts", "ghost-export:help": "node scripts/ghost.js --help", "ghost-sync": "node scripts/ghost.js", "ghost-sync:help": "node scripts/ghost.js --help", "ghost-sync:sponsors": "node scripts/ghost.js --page github-sponsors", "gh-sponsors": "node scripts/gh.js", "gh-sponsors:help": "node scripts/gh.js --help", "gh-repos": "node scripts/gh.js --repos", "gh-stats": "node scripts/gh.js --stats", "sponsors:refresh": "node scripts/gh.js && node scripts/ghost.js --page github-sponsors", "sponsors:dry-run": "node scripts/gh.js --dry-run", "repos:refresh": "node scripts/gh.js --repos && node scripts/ghost.js --page open-source", "open-source:refresh": "node scripts/gh.js --repos && node scripts/ghost.js --page open-source"}, "dependencies": {"@octokit/rest": "^22.0.0", "@tryghost/admin-api": "^1.14.0", "@tryghost/content-api": "^1.12.0", "commander": "^11.1.0", "github-api": "^3.4.0", "handlebars": "^4.7.8", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "marked": "^12.0.0", "node-fetch": "^2.7.0"}, "keywords": ["hugo", "ghost", "migration", "blog"], "author": "<PERSON> <<EMAIL>>", "license": "MIT"}